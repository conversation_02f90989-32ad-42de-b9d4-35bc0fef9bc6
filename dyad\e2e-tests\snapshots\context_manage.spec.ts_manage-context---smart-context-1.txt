{"body": {"model": "gemini/gemini-2.5-pro", "max_tokens": 65535, "temperature": 0, "messages": [{"role": "system", "content": "[[SYSTEM_MESSAGE]]"}, {"role": "user", "content": "[dump]"}], "stream": true, "thinking": {"type": "enabled", "include_thoughts": true, "budget_tokens": 4000}, "dyad_options": {"files": [{"path": "a.ts", "content": "// a.ts\n", "force": true}, {"path": "manual/baz.json", "content": "// File contents excluded from context", "force": true}, {"path": "manual/file.ts", "content": "", "force": true}, {"path": "manual/sub-manual/sub-manual.js", "content": "", "force": true}, {"path": "src/components/ui/helper.ts", "content": "// helper.ts\n", "force": false}, {"path": "src/foo.ts", "content": "// foo.ts\n", "force": false}, {"path": "src/sub/sub1.ts", "content": "// sub/sub1.ts\n", "force": false}, {"path": "src/sub/sub2.tsx", "content": "// sub/sub2.tsx\n", "force": false}, {"path": "src/very-large-file.ts", "content": "// very-large-file.ts\n\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n// 1234567890\n", "force": false}], "enable_lazy_edits": true, "enable_smart_files_context": true}}, "headers": {"authorization": "Bear<PERSON> <PERSON><PERSON><PERSON><PERSON>"}}