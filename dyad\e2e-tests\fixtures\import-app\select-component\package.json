{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.514.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8"}, "devDependencies": {"@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.9.0", "tw-animate-css": "^1.3.4", "typescript": "^5.5.3", "vite": "^6.3.4"}}