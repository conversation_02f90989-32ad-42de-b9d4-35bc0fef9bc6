{"version": "6", "dialect": "sqlite", "id": "553360d1-7173-4bb0-9f31-ab49a0010279", "prevId": "035de440-2d81-4a70-8068-ad4702c9fe32", "tables": {"apps": {"name": "apps", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "github_org": {"name": "github_org", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_repo": {"name": "github_repo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_branch": {"name": "github_branch", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "supabase_project_id": {"name": "supabase_project_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "vercel_project_id": {"name": "vercel_project_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "vercel_project_name": {"name": "vercel_project_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "vercel_team_id": {"name": "vercel_team_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "vercel_deployment_url": {"name": "vercel_deployment_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "chat_context": {"name": "chat_context", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "chats": {"name": "chats", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "app_id": {"name": "app_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "initial_commit_hash": {"name": "initial_commit_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {"chats_app_id_apps_id_fk": {"name": "chats_app_id_apps_id_fk", "tableFrom": "chats", "tableTo": "apps", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "language_model_providers": {"name": "language_model_providers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "api_base_url": {"name": "api_base_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "env_var_name": {"name": "env_var_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "language_models": {"name": "language_models", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "api_name": {"name": "api_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "builtin_provider_id": {"name": "builtin_provider_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_provider_id": {"name": "custom_provider_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "max_output_tokens": {"name": "max_output_tokens", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "context_window": {"name": "context_window", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {"language_models_custom_provider_id_language_model_providers_id_fk": {"name": "language_models_custom_provider_id_language_model_providers_id_fk", "tableFrom": "language_models", "tableTo": "language_model_providers", "columnsFrom": ["custom_provider_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "chat_id": {"name": "chat_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "approval_state": {"name": "approval_state", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "commit_hash": {"name": "commit_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {"messages_chat_id_chats_id_fk": {"name": "messages_chat_id_chats_id_fk", "tableFrom": "messages", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}